import {
   AfterViewInit,
   ChangeDetectorRef,
   Component,
   inject,
   Input,
   OnChanges,
   OnInit,
   SimpleChanges,
   ViewChild
} from '@angular/core';
import {
   UntypedFormArray,
   UntypedFormBuilder,
   UntypedFormControl,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {catchError, Observable, of, tap} from 'rxjs';
import {
   debounceTime,
   distinctUntilChanged,
   filter,
   map,
   startWith,
   switchMap
} from 'rxjs/operators';
import {assert, cmpName, DEBOUNCE_TIME, equalIdentifiables} from '../../utility/utility';
import {
   CustomerSelectComponent
} from '../inputs/customer-select/customer-select.component';
import {Reservation, ReservationStatus} from '../../data/reservation';
import {Room} from '../../data/room';
import {ReservationService} from '../../services/reservation.service';
import {
   fixDates,
   getReservationPrice,
   roomCapacityFull
} from '../../utility/reservation-utility';
import {Money} from '../../data/common';
import {Consumable} from '../../data/bundles/consumable';
import {moneyFormGroup, touchAllFormFields} from '../../utility/form-utility';
import {
   CustomerListInputComponent
} from '../inputs/customer-list-input/customer-list-input.component';
import {CustomerGroupService} from '../../services/customer-group.service';
import {MatDateRangeInput} from '@angular/material/datepicker';
import {DateTime} from 'luxon';
import {Voucher, VoucherDiscount} from '../../data/voucher';
import {validMoney} from '../../utility/money-utility';
import {MatSnackBar} from '@angular/material/snack-bar';
import {Privilege} from '../../data/auth/operator';
import {AuthService} from '../../auth/auth.service';
import {Customer} from '../../data/customers/customer';
import {
   IdScannerResult
} from '../../components/id-scanner-icon/id-scanner-icon.component';
import {ReservationSource} from 'src/app/data/reservation-source';

@Component({
   selector: 'app-reservation-form',
   templateUrl: './reservation-form.component.html',
   styleUrls: ['./reservation-form.component.scss'],
   standalone: false
})
export class ReservationFormComponent implements OnInit, OnChanges, AfterViewInit {
   @ViewChild('reservationDates') reservationDates!: MatDateRangeInput<any>;
   @ViewChild(CustomerSelectComponent) customerSelect!: CustomerSelectComponent;
   @ViewChild(CustomerListInputComponent) guestList?: CustomerListInputComponent;

   @Input() data: Partial<Reservation> = {};
   @Input() edit = false;
   @Input() roomType: Consumable | null = null;
   @Input() isLeisure = false;
   @Input() disabled = false;

   form = this.fb.group({
      id: null,
      titular: [null, Validators.required],
      room: ['', Validators.required],
      guests: this.fb.array([]),
      guestGroupCount: this.fb.group({}),
      start: ['', Validators.required],
      end: ['', Validators.required],
      bundle: [null, Validators.required],
      manualPrice: moneyFormGroup(this.fb),
      status: ReservationStatus.pending,
      color: '',
      source: ['', Validators.required],
      invoiceReceiver: '',
      voucherDiscount: null,
      otaIdentifier: null
   });

   price$: Observable<Money | null> | undefined;
   reservation$: Observable<Partial<Reservation>> | undefined;
   rooms$: Observable<Room[]> | undefined;
   vouchers: Voucher[] = [];
   defaultGuestGroup: UntypedFormControl | null = null;

   equalRooms = equalIdentifiables;
   roomCapacityFull = roomCapacityFull;

   private sReservation = inject(ReservationService);
   private sCustomerGroup = inject(CustomerGroupService);
   private sAuth = inject(AuthService);
   private snackbar = inject(MatSnackBar);
   private cd = inject(ChangeDetectorRef);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): Reservation {
      const {manualPrice, ...rest} = this.form.getRawValue();
      const result: any = rest;

      if (validMoney(manualPrice)) {
         result.manualPrice = manualPrice;
      }

      if (this.isLeisure) {
         result.status = ReservationStatus.ongoing;
      }

      return this.isLeisure ? result : fixDates(result);
   }

   get bundle(): UntypedFormControl {
      return this.form.get('bundle') as UntypedFormControl;
   }

   get guests(): UntypedFormArray {
      return this.form.get('guests') as UntypedFormArray;
   }

   get guestGroupCount(): UntypedFormGroup {
      return this.form.get('guestGroupCount') as UntypedFormGroup;
   }

   get titular(): UntypedFormControl {
      return this.form.get('titular') as UntypedFormControl;
   }

   get manualPrice(): UntypedFormGroup {
      return this.form.get('manualPrice') as UntypedFormGroup;
   }

   get color(): UntypedFormControl {
      return this.form.get('color') as UntypedFormControl;
   }

   get source(): UntypedFormControl {
      return this.form.get('source') as UntypedFormControl;
   }

   get allowSourceIdentifier(): boolean {
      const source = this.source.value as ReservationSource;
      return source && !source.isDefault
   }

   get otaIdentifier(): UntypedFormControl {
      return this.form.get('otaIdentifier') as UntypedFormControl;
   }

   get invoiceReceiver(): UntypedFormControl {
      return this.form.get('invoiceReceiver') as UntypedFormControl;
   }

   get voucherDiscount(): VoucherDiscount | undefined {
      return this.form.get('voucherDiscount')?.value;
   }

   ngOnInit(): void {
      const {guests, guestGroupCount, ...rest} = this.data;
      this.form.patchValue(rest);

      if (guestGroupCount) {
         Object.entries(guestGroupCount).forEach(([id, count]) =>
            this.guestGroupCount.addControl(id, this.fb.control(count)));
      }

      guests?.forEach(
         guest => this.guests.controls.push(new UntypedFormControl(guest)));

      const roomTypeSortFn = (r1: Room, r2: Room): number => {
         if (!this.roomType) {
            return 0;
         }

         if (r2.baseConsumable.id === this.roomType.id) {
            return 1;
         } else if (r1.baseConsumable.id === this.roomType.id) {
            return -1;
         }

         return 0;
      };

      this.source.valueChanges.subscribe(source => {
         const value = source as ReservationSource;
         if (value.requireIdentifier && this.allowSourceIdentifier) {
            this.otaIdentifier.addValidators(Validators.required);
         } else {
            this.otaIdentifier.removeValidators(Validators.required);
         }
      })

      this.rooms$ = this.form.valueChanges.pipe(
         startWith(this.data),
         filter(v => !!v.start && !!v.end),
         map(reservation => this.isLeisure ? {...reservation, isLeisure: this.isLeisure} :
            fixDates(reservation)),
         distinctUntilChanged(
            (v1, v2) => v1.start.equals(v2.start) && v1.end.equals(v2.end)),
         switchMap(() => this.sReservation.getAvailableRooms(this.value)),
         map(rooms => rooms.sort(cmpName).sort(roomTypeSortFn)),
      );

      this.price$ = this.form.valueChanges.pipe(
         startWith(this.data),
         filter(r => r.room && r.bundle && r.start && r.end),
         map(reservation => this.isLeisure ? {...reservation, isLeisure: this.isLeisure} :
            fixDates(reservation)),
         switchMap(res => getReservationPrice(res, this.sReservation).pipe(
            catchError(() => of(null))
         )),
         tap(price => price && this.snackbar.dismiss()),
      );

      this.form.valueChanges.pipe(
         startWith(this.data),
         filter(r => r.titular && r.room && r.bundle && r.start && r.end && r.source),
         map(reservation => this.isLeisure ? reservation : fixDates(reservation)),
         switchMap(res => this.sReservation.getVouchers(res)),
      ).subscribe(vouchers => this.setVouchers(vouchers));

      this.bundle.valueChanges.subscribe(newValue => {
         if (!newValue) {
            this.setVouchers([]);
         }
      });

      this.titular.valueChanges.subscribe(c => this.guestList?.pushGuest(c, true));

      this.sCustomerGroup.getAll().subscribe(cgs => {
         const control = this.guestGroupCount;

         cgs.forEach(
            g => control.get(g.id) || control.addControl(g.id, this.fb.control(0)));

         const defaultGroup = cgs.find(cg => cg.minAge === null && cg.maxAge === null);
         assert(defaultGroup, 'No default group found');

         this.defaultGuestGroup = control.get(defaultGroup.id) as UntypedFormControl;
      });

      this.reservation$ = this.form.valueChanges.pipe(
         startWith(this.value),
         debounceTime(DEBOUNCE_TIME * 2),
         map(r => ({...r, isLeisure: this.isLeisure}))
      );

      if (this.disabled) {
         this.form.disable();
      }

      if (!this.sAuth.privileges[Privilege.manualPrice]) {
         this.manualPrice.disable();
      }
   }

   ngOnChanges(changes: SimpleChanges): void {
      const {isLeisure} = changes;
      if (isLeisure && this.reservationDates) {
         const {start, end} = this.form.controls;
         const amountCtrl = this.manualPrice.get('amount') as UntypedFormGroup;
         if (isLeisure.currentValue) {
            const begin = DateTime.local().set({hour: 14});
            start.setValue(begin);
            end.setValue(begin.plus({second: 1}));
            this.reservationDates.disabled = true;
         } else {
            this.reservationDates.disabled = false;
         }
         amountCtrl.updateValueAndValidity();
      }
   }

   ngAfterViewInit(): void {
      if (this.isLeisure) {
         this.reservationDates.disabled = true;
         this.cd.detectChanges();
      }
   }

   updateCount(count: number): void {
      const currentCount = this.defaultGuestGroup?.value;
      this.defaultGuestGroup?.setValue(Math.max(currentCount, count));
   }

   triggerValidation(): void {
      touchAllFormFields(this.form);
   }

   updateVoucher(voucher: VoucherDiscount): void {
      this.form.patchValue({voucherDiscount: voucher});
   }

   handleIdScanned({action, customer}: IdScannerResult): void {
      this.guestList?.pushGuest(customer);
      if (action === 'save-guest-titular') {
         this.customerSelect.setCustomer(customer);
      }
   }

   handleCustomerChange(customer: Customer): void {
      if (this.customerSelect.value?.id == customer.id) {
         this.customerSelect.setCustomer(customer);
      }

      this.guestList?.updateGuest(customer);
   }

   private setVouchers(vouchers: Voucher[]) {
      this.vouchers = vouchers;
      const currentVoucher = this.voucherDiscount?.voucher;
      if (currentVoucher) {
         const existingVoucher = vouchers.find(v => equalIdentifiables(v, currentVoucher)
            && v.multiplier === currentVoucher.multiplier
            && v.price.amount === currentVoucher.price.amount);
         if (existingVoucher === undefined) {
            this.form.patchValue({voucherDiscount: null});
         }
      }
   }
}
