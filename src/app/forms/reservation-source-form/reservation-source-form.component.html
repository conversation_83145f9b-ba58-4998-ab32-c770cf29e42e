<form [formGroup]="form" class="two-column-grid">
  <mat-form-field>
    <mat-icon matPrefix>source</mat-icon>
    <mat-label>Име</mat-label>
    <input autocomplete="off" formControlName="name" matInput type="text">
  </mat-form-field>
  
  <mat-form-field>
    <mat-icon matPrefix>code</mat-icon>
    <mat-label>Код</mat-label>
    <input autocomplete="off" formControlName="code" matInput type="text">
  </mat-form-field>
  

  
  <mat-form-field>
    <mat-icon matPrefix>link</mat-icon>
    <mat-label>Канален код</mat-label>
    <mat-select formControlName="channelCode">
      <mat-option [value]="null">Няма</mat-option>
      @for (sourceCode of sourceCodes; track sourceCode.value) {
        <mat-option [value]="sourceCode.value">{{sourceCode.label}}</mat-option>
      }
    </mat-select>
  </mat-form-field>
  
  <div class="flex-column" style="grid-column: span 2;">
    <mat-slide-toggle color="primary" formControlName="requireIdentifier">
      Изисква идентификатор
    </mat-slide-toggle>
  </div>
</form>
