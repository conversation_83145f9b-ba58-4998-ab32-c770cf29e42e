import {Component, inject, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';

interface SettingsItem {
   name: string;
   icon: string;
   link: string;
}

@Component({
   selector: 'app-settings',
   templateUrl: './settings.component.html',
   styles: [`
      .settings-container {
         display: flex;
         flex-direction: row;

         .settings-list {
            width: 250px;
            min-width: 250px;
         }

         .setting-display {
            padding: 20px;
            width: 100%;
         }
      }
   `],
   standalone: false
})
export class SettingsComponent implements OnInit {
   settingsItems: SettingsItem[] = [
      {name: 'Стаи', icon: 'door_front', link: 'rooms'},
      {name: 'Цени', icon: 'paid', link: 'pricing'},
      {name: 'Пакети', icon: 'spa', link: 'bundles'},
      {name: 'Отстъпки', icon: 'discount', link: 'discounts'},
      {name: 'Почистване', icon: 'cleaning_services', link: 'cleaning'},
      {name: 'Канали', icon: 'source', link: 'sources'},
      {name: 'Служители', icon: 'supervisor_account', link: 'operators'},
      {name: 'Фискализация', icon: 'point_of_sale', link: 'fiscal'},
      {name: 'Хотел', icon: 'business', link: 'business'},
   ];

   router = inject(Router);
   private route = inject(ActivatedRoute);

   ngOnInit(): void {
      if (this.router.url.endsWith('settings')) {
         this.loadSetting(this.settingsItems[0].link);
      }
   }

   loadSetting(link: string): void {
      this.router.navigate([link], {relativeTo: this.route}).then();
   }
}
