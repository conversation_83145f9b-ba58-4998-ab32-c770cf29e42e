import {Pipe, PipeTransform} from '@angular/core';
import {CustomerDiscountType} from '../data/customers/customer-discount';

@Pipe({
   name: 'accommodationConditionType',
   standalone: false
})
export class AccommodationConditionTypePipe implements PipeTransform {
   transform(value: CustomerDiscountType): string {
      switch (value) {
         case CustomerDiscountType.reservationDuration:
            return 'Брой нощувки';
         case CustomerDiscountType.customerCount:
            return 'Брой гости';
         case CustomerDiscountType.oldCustomer:
            return 'Стар клиент';
         case CustomerDiscountType.roomConsumable:
            return 'Тип стая';
         case CustomerDiscountType.reservationSources:
            return 'Канали за резервация';
      }
   }
}
